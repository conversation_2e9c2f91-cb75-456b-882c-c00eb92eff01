# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT_ID=your-project-id
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json

# Gemini API Configuration
GEMINI_API_KEY=your-gemini-api-key

# API Configuration
API_SECRET_KEY=your-secret-key-for-jwt
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=false

# Database Configuration (optional)
REDIS_URL=redis://localhost:6379

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,https://your-frontend-domain.com