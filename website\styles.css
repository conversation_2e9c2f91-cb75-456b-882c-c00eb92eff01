/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #1a1a1a;
    background-color: #ffffff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #e5e5e5;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 1.25rem;
}

.logo-icon {
    font-size: 1.5rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-menu a {
    text-decoration: none;
    color: #4a4a4a;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #6366f1;
}

.btn-outline {
    padding: 0.5rem 1rem;
    border: 1px solid #6366f1;
    border-radius: 8px;
    color: #6366f1 !important;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline:hover {
    background-color: #6366f1;
    color: white !important;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: #1a1a1a;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    padding: 8rem 0 4rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    overflow: hidden;
}

.hero-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    margin-bottom: 4rem;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.gradient-text {
    background: linear-gradient(45deg, #fbbf24, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-primary {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background-color: #fbbf24;
    color: #1a1a1a;
    padding: 1rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary:hover {
    background-color: #f59e0b;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(251, 191, 36, 0.3);
}

.btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    padding: 1rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Hero Visual */
.hero-visual {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

.demo-window {
    background: #1e1e1e;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 100%;
}

.window-header {
    background: #2d2d2d;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.window-controls {
    display: flex;
    gap: 0.5rem;
}

.control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.control.red { background: #ff5f57; }
.control.yellow { background: #ffbd2e; }
.control.green { background: #28ca42; }

.window-title {
    color: #ffffff;
    font-size: 0.875rem;
}

.window-content {
    padding: 1.5rem;
    font-family: 'Monaco', 'Consolas', monospace;
}

.error-code {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.line-number {
    color: #666;
    font-size: 0.875rem;
}

.error-text {
    color: #ff6b6b;
    font-size: 0.875rem;
}

.translation-arrow {
    text-align: center;
    color: #fbbf24;
    margin: 1rem 0;
    font-weight: 600;
}

.solution-panel {
    background: #2a2a2a;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #28ca42;
}

.solution-panel h4 {
    color: #28ca42;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.solution-panel code {
    display: block;
    color: #ffffff;
    background: #1e1e1e;
    padding: 0.5rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.confidence {
    color: #fbbf24;
    font-size: 0.75rem;
}

/* Features Section */
.features {
    padding: 5rem 0;
    background-color: #f8fafc;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

.section-header p {
    font-size: 1.125rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

.feature-card p {
    color: #6b7280;
    line-height: 1.6;
}

/* Demo Section */
.demo {
    padding: 5rem 0;
    background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
    color: white;
}

.demo-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.demo-steps {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.step {
    display: flex;
    gap: 1rem;
}

.step-number {
    background: #fbbf24;
    color: #1a1a1a;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
}

.step-content h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
}

.step-content p {
    opacity: 0.9;
    line-height: 1.6;
}

.demo-video {
    display: flex;
    justify-content: center;
}

.video-placeholder {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 4rem 2rem;
    text-align: center;
    width: 100%;
    max-width: 400px;
}

.play-button {
    background: #fbbf24;
    color: #1a1a1a;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.play-button:hover {
    transform: scale(1.1);
}

/* Pricing Section */
.pricing {
    padding: 5rem 0;
    background-color: #f8fafc;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.pricing-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.pricing-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.pricing-card.popular {
    border: 2px solid #6366f1;
    transform: scale(1.05);
}

.plan-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: #6366f1;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.plan-header {
    text-align: center;
    margin-bottom: 2rem;
}

.plan-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.price {
    font-size: 3rem;
    font-weight: 700;
    color: #6366f1;
}

.price span {
    font-size: 1rem;
    color: #6b7280;
}

.plan-features {
    list-style: none;
    margin-bottom: 2rem;
}

.plan-features li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.plan-button {
    width: 100%;
    text-align: center;
    padding: 1rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

/* Installation Section */
.install {
    padding: 5rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.install-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.install-option {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
    backdrop-filter: blur(10px);
}

.install-option h3 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

.install-option p {
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.install-commands {
    background: rgba(0, 0, 0, 0.3);
    padding: 1rem;
    border-radius: 8px;
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 0.875rem;
}

/* Footer */
.footer {
    background-color: #1a1a1a;
    color: #ffffff;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    margin-bottom: 1rem;
    font-weight: 600;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #fbbf24;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    color: #d1d5db;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: #fbbf24;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 2rem;
    text-align: center;
    color: #9ca3af;
}

.footer-bottom a {
    color: #fbbf24;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        display: none;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .demo-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .container {
        padding: 0 1rem;
    }
    
    .nav-container {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 6rem 0 3rem;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
    
    .features-grid,
    .pricing-grid,
    .install-steps {
        grid-template-columns: 1fr;
    }
}